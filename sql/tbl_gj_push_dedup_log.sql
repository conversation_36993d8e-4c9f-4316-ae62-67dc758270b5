-- 预警推送去重日志表
CREATE TABLE `tbl_gj_push_dedup_log` (
  `id` bigint NOT NULL COMMENT '主键，自动递增',
  `api_call_logs_id` bigint NOT NULL COMMENT '推送日志 id',
  `warn_model` tinyint(4) NOT NULL COMMENT '预警模型：1-模型1，2-模型2',
  `qymc` varchar(255) DEFAULT NULL COMMENT '企业名称（用于模型1去重）',
  `jbxx_id` varchar(64) DEFAULT NULL COMMENT '企业基本信息ID（用于模型2去重，对应view_cq_jbxxb.id）',
  `create_time` datetime NOT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`api_call_logs_id`),
  KEY `idx_warn_model_qymc` (`warn_model`, `qymc`),
  KEY `idx_warn_model_jbxx_id` (`warn_model`, `jbxx_id`)
) COMMENT='预警模型推送日志表';
