# 预警推送去重功能实现总结

## 实现概述

根据需求，为 `GjPushMsgHandler` 相关的类实现了预警模型推送的去重逻辑：

- **模型1去重逻辑**：根据推送的企业名称判断该企业是否已经推送过预警消息
- **模型2去重逻辑**：使用企业最新审核通过的登记信息作为判断依据（登记id = view_cq_jbxxb.id 对应实体类是 JbxxbBO.jbQyId）

## 新增文件列表

### 1. 实体类
- `gzwcq-schedule/src/main/java/com/zjhc/gzwcq/job/zjh/entity/gjPush/entity/GjPushDedupLog.java`
  - 预警推送去重日志表实体类
  - 包含字段：id、apiCallLogsId、warnModel、qymc、jbxxId、createTime

### 2. Mapper 接口
- `gzwcq-schedule/src/main/java/com/zjhc/gzwcq/job/zjh/mapper/IGjPushDedupLogMapper.java`
  - 提供插入去重日志、检查模型1重复、检查模型2重复的方法

### 3. Mapper XML 文件
- `gzwcq-schedule/src/main/java/com/zjhc/gzwcq/job/zjh/mapper/GjPushDedupLogMapper.xml`
  - 实现具体的 SQL 查询逻辑

### 4. Service 接口
- `gzwcq-schedule/src/main/java/com/zjhc/gzwcq/job/zjh/service/api/IGjPushDedupLogService.java`
  - 定义去重服务的接口方法

### 5. Service 实现类
- `gzwcq-schedule/src/main/java/com/zjhc/gzwcq/job/zjh/service/impl/GjPushDedupLogServiceImpl.java`
  - 实现去重逻辑的具体业务方法

## 修改的文件

### 1. GjPushMsgServiceImpl.java
- 添加了去重服务的依赖注入
- 在 `pushMsg` 方法中集成去重逻辑：
  - 在推送前过滤重复数据
  - 推送成功后记录去重日志

## 核心功能实现

### 1. 去重逻辑
```java
// 模型1：根据企业名称去重
if (WarnModelEnum.MODEL_1.equals(warnModelEnum)) {
    int count = gjPushDedupLogMapper.checkModel1Duplicate(warnModelEnum.getCode(), jbxxbBO.getJbQymc());
    return count > 0;
}

// 模型2：根据登记ID去重  
if (WarnModelEnum.MODEL_2.equals(warnModelEnum)) {
    int count = gjPushDedupLogMapper.checkModel2Duplicate(warnModelEnum.getCode(), jbxxbBO.getJbQyId());
    return count > 0;
}
```

### 2. 推送流程
1. 获取待推送数据
2. 根据预警模型类型进行去重过滤
3. 如果有数据需要推送，执行推送逻辑
4. 推送成功后，记录去重日志

### 3. 日志记录
- 只有推送成功（status == 1）时才记录去重日志
- 推送失败不记录去重日志
- 批量保存去重日志，每个企业一条记录

## 数据库表结构

参考 `sql/tbl_gj_push_dedup_log.sql` 文件：
- 主键：id (varchar(50))
- 推送日志ID：api_call_logs_id (varchar(64))
- 预警模型：warn_model (tinyint(4)) - 1:模型1, 2:模型2
- 企业名称：qymc (varchar(255)) - 用于模型1去重
- 登记ID：jbxx_id (varchar(64)) - 用于模型2去重
- 创建时间：create_time (datetime)

## 使用说明

1. 确保数据库中已创建 `tbl_gj_push_dedup_log` 表
2. 重启 gzwcq-schedule 服务
3. 执行 xxljob 任务时会自动应用去重逻辑
4. 可通过日志查看去重效果和推送结果

## 注意事项

- 去重逻辑基于数据库查询，确保数据库连接正常
- 模型1和模型2使用不同的去重字段，互不影响
- 推送失败时不会记录去重日志，避免影响后续重试
- 所有新增代码都包含了 <AUTHOR> 注释
