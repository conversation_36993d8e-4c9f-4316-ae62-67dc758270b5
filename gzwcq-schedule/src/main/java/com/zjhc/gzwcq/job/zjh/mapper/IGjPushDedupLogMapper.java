package com.zjhc.gzwcq.job.zjh.mapper;

import com.zjhc.gzwcq.job.zjh.entity.gjPush.entity.GjPushDedupLog;
import org.apache.ibatis.annotations.Param;

/**
 * 预警推送去重日志 Mapper 接口
 * <AUTHOR>
 * @date 2025/7/31
 */
public interface IGjPushDedupLogMapper {

    /**
     * 插入去重日志
     * @param gjPushDedupLog 去重日志对象
     * @return 影响行数
     */
    int insert(GjPushDedupLog gjPushDedupLog);

    /**
     * 检查模型1是否已推送过（根据企业名称）
     * @param warnModel 预警模型
     * @param qymc 企业名称
     * @return 记录数量
     */
    int checkModel1Duplicate(@Param("warnModel") Integer warnModel, @Param("qymc") String qymc);

    /**
     * 检查模型2是否已推送过（根据登记ID）
     * @param warnModel 预警模型
     * @param jbxxId 登记ID
     * @return 记录数量
     */
    int checkModel2Duplicate(@Param("warnModel") Integer warnModel, @Param("jbxxId") String jbxxId);
}
