<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IGjPushDedupLogMapper">

    <!-- 插入去重日志 -->
    <insert id="insert" parameterType="com.zjhc.gzwcq.job.zjh.entity.gjPush.entity.GjPushDedupLog">
        INSERT INTO tbl_gj_push_dedup_log (
            id,
            api_call_logs_id,
            warn_model,
            qymc,
            jbxx_id,
            create_time
        ) VALUES (
            #{id},
            #{apiCallLogsId},
            #{warnModel},
            #{qymc},
            #{jbxxId},
            #{createTime}
        )
    </insert>

    <!-- 检查模型1是否已推送过（根据企业名称） -->
    <select id="checkModel1Duplicate" resultType="int">
        SELECT COUNT(1)
        FROM tbl_gj_push_dedup_log
        WHERE warn_model = #{warnModel}
          AND qymc = #{qymc}
    </select>

    <!-- 检查模型2是否已推送过（根据登记ID） -->
    <select id="checkModel2Duplicate" resultType="int">
        SELECT COUNT(1)
        FROM tbl_gj_push_dedup_log
        WHERE warn_model = #{warnModel}
          AND jbxx_id = #{jbxxId}
    </select>

</mapper>
