package com.zjhc.gzwcq.job.zjh.entity.gjPush.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警推送去重日志表
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class GjPushDedupLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自动递增
     */
    private Long id;

    /**
     * 推送日志 id
     */
    private Long apiCallLogsId;

    /**
     * 预警模型：1-模型1，2-模型2
     */
    private Integer warnModel;

    /**
     * 企业名称（用于模型1去重）
     */
    private String qymc;

    /**
     * 企业基本信息ID（用于模型2去重，对应view_cq_jbxxb.id）
     */
    private String jbxxId;

    /**
     * 推送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public GjPushDedupLog() {
        super();
    }

    public GjPushDedupLog(Long apiCallLogsId, Integer warnModel, String qymc, String jbxxId) {
        this.apiCallLogsId = apiCallLogsId;
        this.warnModel = warnModel;
        this.qymc = qymc;
        this.jbxxId = jbxxId;
        this.createTime = new Date();
    }
}
