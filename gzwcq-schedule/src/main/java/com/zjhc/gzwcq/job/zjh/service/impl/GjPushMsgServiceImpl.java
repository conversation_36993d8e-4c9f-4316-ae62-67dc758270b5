package com.zjhc.gzwcq.job.zjh.service.impl;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.job.zjh.entity.enums.WarnModelEnum;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.DicWarningBO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.request.DataDTO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.request.GjPushDTO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.request.ValueDTO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.response.PushVO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.vo.OrgInfoVO;
import com.zjhc.gzwcq.job.zjh.mapper.IApiCallLogsMapper;
import com.zjhc.gzwcq.job.zjh.mapper.IGjPushMsgMapper;
import com.zjhc.gzwcq.job.zjh.service.api.IGjPushMsgService;
import com.zjhc.gzwcq.job.zjh.service.api.IGjPushDedupLogService;
import com.zjhc.gzwcq.job.zjh.util.DateUtils;
import com.zjhc.gzwcq.job.zjh.util.GjPushOAuth2TokenUtil;
import com.zjhc.gzwcq.job.zjh.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import sun.security.rsa.RSAUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/06/13 10:24:14
 **/
@Service
@Slf4j
public class GjPushMsgServiceImpl implements IGjPushMsgService {
    @Autowired
    private IGjPushMsgMapper gjPushMsgMapper;
    private final static Integer PAGE_SIZE = 1000;
    private final static String EARLY_WARNING = "EARLY_WARNING";
    private Map<String, DicWarningBO> dicWarningMap = null;
    @Value("${GjPush.config.authServerUrl}")
    private String authServerUrl; // 授权服务器地址
    @Value("${GjPush.config.appId}")
    private String appId;
    @Value("${GjPush.config.appSecret}")
    private String appSecret;
    @Value("${GjPush.config.resourceCode}")
    private String resourceCode;
    @Value("${GjPush.config.pushServerUrl}")
    private String pushServerUrl; // 授权服务器地址
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private IApiCallLogsMapper apiCallLogsMapper;
    @Autowired
    private IGjPushDedupLogService gjPushDedupLogService;
    //来源系统
    private static final String SOURCE_SYSTEM_NO = "0900";
    //来源规则
    private static final String SOURCE_RULE_NO = "M100141";
    //上报单位
    private static final String ORG_CODE  = "91330000765209674T";
    @Override
    public void pushMsg(WarnModelEnum warnModelEnum) {
        String enterprise = "enterprise";
        List<JbxxbBO> jbxxbBOS = initMsg(warnModelEnum);
        if (jbxxbBOS.isEmpty()) {
            log.info("没有需要推送的数据");
            return;
        }

        // 去重逻辑：过滤已推送的数据
        List<JbxxbBO> filteredJbxxbBOS = gjPushDedupLogService.filterDuplicates(warnModelEnum, jbxxbBOS);
        if (filteredJbxxbBOS.isEmpty()) {
            log.info("所有数据都已推送过，跳过本次推送。预警模型：{}", warnModelEnum.getDesc());
            return;
        }

        log.info("预警模型：{}，原始数据量：{}，去重后数据量：{}",
                warnModelEnum.getDesc(), jbxxbBOS.size(), filteredJbxxbBOS.size());
        //构建value数据
        List<ValueDTO> valueDTO = filteredJbxxbBOS.stream().map(item -> {
             String parentsId = item.getParentsId();
            String jbQymc = item.getJbQymc();
            String jbQyId = item.getJbQyId();
            String jbQybm = item.getJbQybm();
            if ( StringUtils.isNotBlank(jbQybm)){
                String[] s = jbQybm.split("_");
                jbQybm = s.length > 1 ? s[1]:jbQybm;
            }

            OrgInfoVO org = gjPushMsgMapper.getOrgName(parentsId);
            String orgCode = org.getOrgCode();
            if ( StringUtils.isNotBlank(orgCode)){
                String[] s = orgCode.split("_");
                orgCode = s.length > 1 ? s[1]:orgCode;
            }
            String orgName = org.getOrgName();
            StringBuilder sourceMessag = new StringBuilder("【"+jbQymc+"】，名称中含有");
            List<JSONObject> sourceContents = new ArrayList<>();
            JSONObject sourceContent = new JSONObject();
            sourceContent.put("doc_type", enterprise);
            sourceContent.put("doc_id", jbQyId);
            if (dicWarningMap == null){
                dicWarningMapInfo();
            }
            DicWarningBO dicWarningBO = dicWarningMap.get(parentsId);
            String containsKeywordsKeyword = getContainsKeywordsKeyword(jbQymc, Arrays.stream(dicWarningBO.getText().split(",")).collect(Collectors.toList()));
            sourceMessag.append("【");
            sourceMessag.append(containsKeywordsKeyword);
            sourceMessag.append("】字号，与《国有企业参股管理暂行办法》有关规定不符。");
            String msg = sourceMessag.toString();
            String warning_time = DateUtils.format(new Date(), DateUtils.YYYYMMDDHHMMSS);
            String unique_key = UUID.randomUUID().toString();

            sourceContent.put("source_message",  new StringBuilder("@unique_key|预警编号|unique_key|").toString());
            sourceContents.add(sourceContent);
            return new ValueDTO(SOURCE_SYSTEM_NO, unique_key, SOURCE_RULE_NO, orgCode, orgName, jbQybm, jbQymc, null, null,
                    msg, JSON.toJSONString(sourceContents),warning_time ,  DateUtils.format(new Date(), DateUtils.YYYYMMDDHHMMSS), enterprise);
        }).collect(Collectors.toList());


        List<DataDTO> data = valueDTO.stream().map(item -> {
            return new DataDTO("addOrUpdate", DateUtils.format(new
                    Date(), DateUtils.YYYY_MM_DD_HH_MM_SS), item);

        }).collect(Collectors.toList());
        GjPushDTO gjPushDTO = builderDate(data);
        ApiCallLogs apiCallLogs = new ApiCallLogs();
        String request ="";
        String sendPost = "";
        int status = 1;
        Date executionTime = new Date();
        String erroMessage = null;
        PushVO pushVO = new PushVO();
        Map<String, String> headers = new HashMap<>();
        //获取token
        try {
            GjPushOAuth2TokenUtil gjPushOAuth2TokenUtil = new GjPushOAuth2TokenUtil(restTemplate, authServerUrl, appId, appSecret);
            Map<String, Object> accessToken = gjPushOAuth2TokenUtil.getAccessToken();
            if (accessToken == null){
                throw new Exception("获取token失败接口:"+ authServerUrl);
            }
            String token = accessToken.get("access_token").toString();
            log.info("获取token："+    token);
            headers = new HashMap<>();
            headers.put("Blade-Auth", "Bearer " + token);
            log.info("请求头参数"+ headers);
             request = JSON.toJSONString(gjPushDTO);
            log.info("请求参数："+ request);
            sendPost = HttpUtils.sendPost(pushServerUrl, request, headers);
            System.out.println("sendPost = " + sendPost);
            executionTime = new Date();
            pushVO = JSON.parseObject(sendPost, new TypeReference<PushVO>() {
            });
            if (pushVO.getCode() != 200) {
                status = 2;
                erroMessage = pushVO.getMsg();
                log.error("推送异常："+ erroMessage);
            }
        } catch (Exception e) {
           status = 2;
            erroMessage = e.getMessage();
            log.error("推送异常："+ erroMessage);
        }
        apiCallLogs.setRequestUrl(pushServerUrl);
        apiCallLogs.setType(1);
        apiCallLogs.setAppId(appId);
        apiCallLogs.setApiName("预警平台");
        apiCallLogs.setRequestMethod("POST");
        apiCallLogs.setRequest(request);
        apiCallLogs.setResponse(sendPost);
        apiCallLogs.setResponseStatusCode(pushVO.getCode());
        apiCallLogs.setStatus(status);
        apiCallLogs.setExecutionTimeMs(executionTime.getTime());
        apiCallLogs.setCreateTime(new Date());
        apiCallLogs.setLastUpdateTime(new Date());
        apiCallLogs.setErrMsg(erroMessage);
        apiCallLogs.setRequestHeaders(JSON.toJSONString(headers));
        apiCallLogsMapper.insert(apiCallLogs);

        // 推送成功后记录去重日志
        if (status == 1) {
            gjPushDedupLogService.batchSaveLog(apiCallLogs.getId(), warnModelEnum, filteredJbxxbBOS);
        }
    }
    /**
    * @description: TODO 构建请求参数
    * @author: hhy
    * @date: 2025/6/17 13:56
    * @param
    * @return com.zjhc.gzwcq.job.zjh.entity.gjPush.request.GjPushDTO
    */
    private GjPushDTO builderDate(List<DataDTO> data) {
        String publicKeyString = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE3FLXDVkd5lAHltuSg7TUY5K7nppP3WoGCXZRQHz0tBuHuKbA/Onj4HqrPTWK3KAmv8Mwr7n0ah2emxFaaI81YQ==";
        SM2 sm2en = SmUtil.sm2(null, publicKeyString);
        // 1. 生成 sm4 128位的密钥。
        byte[] key = SmUtil.sm4().getSecretKey().getEncoded();
//        System.out.println("sm4.key:\n" + Arrays.toString(key));
        // 2. 用公钥加密sm4密钥
        byte[] sm2enData = sm2en.encrypt(key, KeyType.PublicKey);

//        System.out.println("sk byte:\n" + Arrays.toString(sm2enData));

        String sk = cn.hutool.core.codec.Base64.encode(sm2enData);

//        System.out.println("sk:\n" + sk);
        // 6.将变更信息列表转换为JSON字符串
        String dataJson =   JSON.toJSONString(data);

//        System.out.println("dataJson = " + dataJson);
        String encryptData = SmUtil.sm4(key).encryptBase64(dataJson);

        return new GjPushDTO("data",null,null,null,null,ORG_CODE,resourceCode,SOURCE_SYSTEM_NO,
                null, sk, encryptData);

    }
    /**
     * @param
     * @return java.util.List<com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO>
     * @description: TODO 初始化推送信息
     * @author: hhy
     * @date: 2025/6/13 14:12
     */
    @Override
    public List<JbxxbBO> initMsg(WarnModelEnum warnModelEnum) {
        List<JbxxbBO> vo = new ArrayList<>();
        Long toal = gjPushMsgMapper.selectJbxxbBOToal(warnModelEnum);
        if (toal == 0) {
            return null;
        }
        //分页查询 计算分几页
        int pageNum = (int) ((toal + PAGE_SIZE - 1) / PAGE_SIZE);
        for (int i = 1; i <= pageNum; i++) {
            PageHelper.startPage(i, PAGE_SIZE);
            List<JbxxbBO> jbxxbBOS = gjPushMsgMapper.selectJbxxbBO(warnModelEnum);
            if (jbxxbBOS.isEmpty()) continue;

            //检查数据
            List<JbxxbBO> bo = this.checkMsg(jbxxbBOS);
            if (!bo.isEmpty()) {
                vo.addAll(bo);
            }
        }
        return vo;
    }

    /**
     * @param jbxxbBOS 参数说明
     * @return java.util.List<com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO>
     * @description: TODO 检查数据保留需要推送的数据
     * @author: hhy
     * @date: 2025/6/13 14:23
     */
    @Override
    public List<JbxxbBO> checkMsg(List<JbxxbBO> jbxxbBOS) {
        List<JbxxbBO> vo = jbxxbBOS.stream().filter(jbxxbBO -> {
            String jbxxbOrgId = jbxxbBO.getOrgId();
            String jbQymc = jbxxbBO.getJbQymc();
            String parentsId = jbxxbBO.getParentsId();
            if (Objects.isNull(jbQymc) || Objects.isNull(parentsId)) {
                return false;
            }
            DicWarningBO warningList = this.getWarningList(parentsId);
            String orgId = warningList.getOrgId();
            //关键字
            String text = warningList.getText();
            //省属企业不需要
            if (StringUtils.equals(orgId, jbxxbOrgId)) {
                return false;
            }
            if (StringUtils.isBlank(text)) {
                return false;
            }
            return containsKeywords(jbQymc, Arrays.stream(text.split(",")).collect(Collectors.toList()));
        }).collect(Collectors.toList());
        return vo;
    }

    /**
     * @param orgId 参数说明
     * @return java.util.List<com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.DicWarningBO>
     * @description: TODO 传入父组织id  获取预警关键字
     * @author: hhy
     * @date: 2025/6/13 14:57
     */
    private DicWarningBO getWarningList(String orgId) {
        if (Objects.isNull(dicWarningMap)) {
            List<DicWarningBO> dicWarningBO = gjPushMsgMapper.selectEarlyWarning(EARLY_WARNING);
            dicWarningMap = dicWarningBO.stream().collect(Collectors.toMap(item -> item.getOrgId(), item -> item, (k1, k2) -> k1));
        }
        return dicWarningMap.get(orgId);
    }

    /**
     * @param
     * @return void
     * @description: TODO 初始化预警关键字
     * @author: hhy
     * @date: 2025/6/13 15:04
     */
    private void dicWarningMapInfo() {
        if (Objects.isNull(dicWarningMap)) {
            List<DicWarningBO> dicWarningBO = gjPushMsgMapper.selectEarlyWarning(EARLY_WARNING);
            dicWarningMap = dicWarningBO.stream().collect(Collectors.toMap(item -> item.getOrgId(), item -> item, (k1, k2) -> k1));
        }
    }


    /**
     * @param text     要检查的文本
     * @param keywords 关键词列表
     * @return boolean
     * @description: TODO 检查文本中是否包含指定关键词中的任意一个（必须是连续字符）
     * @author: hhy
     * @date: 2025/6/13 15:05
     */
    public boolean containsKeywords(String text, List<String> keywords) {
        if (text == null || keywords == null || keywords.isEmpty()) {
            return false;
        }

        for (String keyword : keywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param text     参数说明
     * @param keywords 参数说明
     * @return java.lang.String
     * @description: TODO 获取关键词
     * @author: hhy
     * @date: 2025/6/17 9:44
     */
    public String getContainsKeywordsKeyword(String text, List<String> keywords) {
        for (String keyword : keywords) {
            if (text.contains(keyword)) {
                return keyword;
            }
        }

        return "";
    }

}
