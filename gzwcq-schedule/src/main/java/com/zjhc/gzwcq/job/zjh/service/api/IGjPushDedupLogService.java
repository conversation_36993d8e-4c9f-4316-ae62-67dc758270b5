package com.zjhc.gzwcq.job.zjh.service.api;

import com.zjhc.gzwcq.job.zjh.entity.enums.WarnModelEnum;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.entity.GjPushDedupLog;

import java.util.List;

/**
 * 预警推送去重日志服务接口
 * <AUTHOR>
 * @date 2025/7/31
 */
public interface IGjPushDedupLogService {

    /**
     * 保存去重日志
     * @param gjPushDedupLog 去重日志对象
     * @return 是否保存成功
     */
    boolean saveLog(GjPushDedupLog gjPushDedupLog);

    /**
     * 检查是否重复推送
     * @param warnModelEnum 预警模型
     * @param jbxxbBO 企业基本信息
     * @return true-重复，false-不重复
     */
    boolean isDuplicate(WarnModelEnum warnModelEnum, JbxxbBO jbxxbBO);

    /**
     * 过滤重复数据
     * @param warnModelEnum 预警模型
     * @param jbxxbBOList 企业基本信息列表
     * @return 过滤后的列表
     */
    List<JbxxbBO> filterDuplicates(WarnModelEnum warnModelEnum, List<JbxxbBO> jbxxbBOList);

    /**
     * 批量保存去重日志
     * @param apiCallLogsId 推送日志ID
     * @param warnModelEnum 预警模型
     * @param jbxxbBOList 企业基本信息列表
     */
    void batchSaveLog(Long apiCallLogsId, WarnModelEnum warnModelEnum, List<JbxxbBO> jbxxbBOList);
}
