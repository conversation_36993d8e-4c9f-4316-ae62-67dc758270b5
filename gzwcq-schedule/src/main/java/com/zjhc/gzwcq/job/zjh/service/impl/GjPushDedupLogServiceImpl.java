package com.zjhc.gzwcq.job.zjh.service.impl;

import com.zjhc.gzwcq.job.zjh.entity.enums.WarnModelEnum;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.entity.GjPushDedupLog;
import com.zjhc.gzwcq.job.zjh.mapper.IGjPushDedupLogMapper;
import com.zjhc.gzwcq.job.zjh.service.api.IGjPushDedupLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 预警推送去重日志服务实现类
 * <AUTHOR>
 * @date 2025/7/31
 */
@Slf4j
@Service
public class GjPushDedupLogServiceImpl implements IGjPushDedupLogService {

    @Autowired
    private IGjPushDedupLogMapper gjPushDedupLogMapper;

    @Override
    public boolean saveLog(GjPushDedupLog gjPushDedupLog) {
        try {
            int result = gjPushDedupLogMapper.insert(gjPushDedupLog);
            return result > 0;
        } catch (Exception e) {
            log.error("保存去重日志失败", e);
            return false;
        }
    }

    @Override
    public boolean isDuplicate(WarnModelEnum warnModelEnum, JbxxbBO jbxxbBO) {
        try {
            if (WarnModelEnum.MODEL_1.equals(warnModelEnum)) {
                // 模型1：根据企业名称判断是否重复
                if (StringUtils.isBlank(jbxxbBO.getJbQymc())) {
                    return false;
                }
                int count = gjPushDedupLogMapper.checkModel1Duplicate(warnModelEnum.getCode(), jbxxbBO.getJbQymc());
                return count > 0;
            } else if (WarnModelEnum.MODEL_2.equals(warnModelEnum)) {
                // 模型2：根据登记ID判断是否重复
                if (StringUtils.isBlank(jbxxbBO.getJbQyId())) {
                    return false;
                }
                int count = gjPushDedupLogMapper.checkModel2Duplicate(warnModelEnum.getCode(), jbxxbBO.getJbQyId());
                return count > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("检查重复推送失败，预警模型：{}，企业信息：{}", warnModelEnum, jbxxbBO, e);
            return false;
        }
    }

    @Override
    public List<JbxxbBO> filterDuplicates(WarnModelEnum warnModelEnum, List<JbxxbBO> jbxxbBOList) {
        if (jbxxbBOList == null || jbxxbBOList.isEmpty()) {
            return jbxxbBOList;
        }

        return jbxxbBOList.stream()
                .filter(jbxxbBO -> !isDuplicate(warnModelEnum, jbxxbBO))
                .collect(Collectors.toList());
    }

    @Override
    public void batchSaveLog(Long apiCallLogsId, WarnModelEnum warnModelEnum, List<JbxxbBO> jbxxbBOList) {
        if (jbxxbBOList == null || jbxxbBOList.isEmpty()) {
            return;
        }

        for (JbxxbBO jbxxbBO : jbxxbBOList) {
            try {
                GjPushDedupLog dedupLog = new GjPushDedupLog(
                        apiCallLogsId,
                        warnModelEnum.getCode(),
                        jbxxbBO.getJbQymc(),
                        jbxxbBO.getJbQyId()
                );
                saveLog(dedupLog);
            } catch (Exception e) {
                log.error("保存去重日志失败，企业信息：{}", jbxxbBO, e);
            }
        }
    }
}
